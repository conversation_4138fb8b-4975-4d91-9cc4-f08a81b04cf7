"""
基于Locust的API压测脚本
支持多种API接口的并发测试，包括LLM问答、RAG问答、数据问答等
"""

import os
import json
import uuid
import time
import random
from typing import Dict, List, Any
from locust import HttpUser, task, between, events
from locust.env import Environment
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 从环境变量获取配置
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8080/api/v1")
API_TOKEN = os.getenv("API_ACCESS_TOKEN", "Bear ipd-OOabxNh6usxsPgTt6EYZHqE1")
TEST_USER_ID = os.getenv("TEST_USER_ID", "load_test_user")

# 测试数据配置
TEST_QUERIES = [
    "器件距离板边最小距离",
    "PDN仿真不通过如何优化", 
    "分板筋距离RF Connectors要求",
    "无工艺边拼板的要求有哪些",
    "板振问题案例",
    "FPC点击微动失效怎么解决",
    "双层FPC的归一化叠层有哪些",
    "ACLR参数定义及设计要点",
    "PCB设计基础知识",
    "HDI板相关知识",
    "库存周转率如何计算？",
    "什么是供应链管理？",
    "如何优化生产流程？",
    "质量控制的关键指标有哪些？"
]

MODEL_IDS = ["qwen3_32b", "qwen3_235b_2507"]

class APILoadTestUser(HttpUser):
    """API压测用户类"""
    
    wait_time = between(1, 3)  # 用户请求间隔时间
    
    def on_start(self):
        """用户开始时的初始化"""
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": API_TOKEN
        }
        self.user_id = f"{TEST_USER_ID}_{uuid.uuid4().hex[:8]}"
        self.conversation_id = str(uuid.uuid4())
        self.history = []
        
        # 健康检查
        self.health_check()
    
    def health_check(self):
        """健康检查"""
        try:
            response = self.client.get("/health", headers=self.headers)
            if response.status_code == 200:
                logger.info(f"健康检查成功: {response.json()}")
            else:
                logger.warning(f"健康检查失败: {response.status_code}")
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
    
    def generate_request_payload(self, endpoint: str, query: str = None) -> Dict[str, Any]:
        """生成请求负载"""
        if not query:
            query = random.choice(TEST_QUERIES)
        
        msg_id = str(uuid.uuid4())
        model_id = random.choice(MODEL_IDS)
        
        base_payload = {
            "query": query,
            "user_id": self.user_id,
            "model_id": model_id,
            "msg_id": msg_id,
            "conversation_id": self.conversation_id,
            "history": self.history[-5:],  # 保留最近5条历史记录
            "stream": False,  # 压测时使用非流式响应，便于准确测量响应时间
            "enable_thinking": False  # 压测时禁用思考模式以提高性能
        }
        
        # 根据不同端点添加特定参数
        if endpoint in ["/rag-qa", "/data-qa", "/all-qa"]:
            base_payload.update({
                "top_k": random.randint(5, 20),
                "top_r": random.randint(3, 10),
                "min_score": round(random.uniform(0.1, 0.8), 2),
                "mode": random.choice(["strict", "common"])
            })
        
        if endpoint == "/search":
            search_payload = {
                "query": query,
                "user_id": self.user_id,
                "msg_id": msg_id,
                "top_k": random.randint(5, 20),
                "top_r": random.randint(3, 10),
                "min_score": round(random.uniform(0.1, 0.8), 2),
                "collections": random.choice([None, ["car"], ["hardware"], ["data"], ["car", "hardware"]])
            }
            return search_payload
        
        return base_payload
    
    @task(10)
    def test_llm_qa(self):
        """测试LLM问答接口（非流式）"""
        payload = self.generate_request_payload("/llm-qa")

        with self.client.post("/llm-qa",
                             json=payload,
                             headers=self.headers,
                             catch_response=True,
                             timeout=60) as response:  # 增加超时时间
            if response.status_code == 200:
                try:
                    result = response.json()
                    # 验证响应结构
                    if "answer" in result and result.get("success", True):
                        # 更新历史记录
                        self.history.append({
                            "query": payload["query"],
                            "content": result.get("answer", "")[:100]  # 截取前100字符
                        })
                        response.success()
                    else:
                        response.failure(f"响应格式错误: {result}")
                except json.JSONDecodeError as e:
                    response.failure(f"JSON解析失败: {str(e)}")
            else:
                response.failure(f"HTTP {response.status_code}: {response.text[:200]}")
    
    @task(15)
    def test_rag_qa(self):
        """测试RAG问答接口（非流式）"""
        payload = self.generate_request_payload("/rag-qa")

        with self.client.post("/rag-qa",
                             json=payload,
                             headers=self.headers,
                             catch_response=True,
                             timeout=60) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    # 验证响应结构
                    if "answer" in result and result.get("success", True):
                        self.history.append({
                            "query": payload["query"],
                            "content": result.get("answer", "")[:100]
                        })
                        response.success()
                    else:
                        response.failure(f"响应格式错误: {result}")
                except json.JSONDecodeError as e:
                    response.failure(f"JSON解析失败: {str(e)}")
            else:
                response.failure(f"HTTP {response.status_code}: {response.text[:200]}")
    
    @task(20)
    def test_data_qa(self):
        """测试数据问答接口（非流式）"""
        payload = self.generate_request_payload("/data-qa")

        with self.client.post("/data-qa",
                             json=payload,
                             headers=self.headers,
                             catch_response=True,
                             timeout=60) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    # 验证响应结构
                    if "answer" in result and result.get("success", True):
                        self.history.append({
                            "query": payload["query"],
                            "content": result.get("answer", "")[:100]
                        })
                        response.success()
                    else:
                        response.failure(f"响应格式错误: {result}")
                except json.JSONDecodeError as e:
                    response.failure(f"JSON解析失败: {str(e)}")
            else:
                response.failure(f"HTTP {response.status_code}: {response.text[:200]}")
    
    @task(8)
    def test_all_qa(self):
        """测试全库问答接口（非流式）"""
        payload = self.generate_request_payload("/all-qa")
        payload["collection"] = random.choice([None, ["car"], ["hardware"], ["data"]])

        with self.client.post("/all-qa",
                             json=payload,
                             headers=self.headers,
                             catch_response=True,
                             timeout=60) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    # 验证响应结构
                    if "answer" in result and result.get("success", True):
                        self.history.append({
                            "query": payload["query"],
                            "content": result.get("answer", "")[:100]
                        })
                        response.success()
                    else:
                        response.failure(f"响应格式错误: {result}")
                except json.JSONDecodeError as e:
                    response.failure(f"JSON解析失败: {str(e)}")
            else:
                response.failure(f"HTTP {response.status_code}: {response.text[:200]}")
    
    @task(5)
    def test_search(self):
        """测试搜索接口（非流式）"""
        payload = self.generate_request_payload("/search")

        with self.client.post("/search",
                             json=payload,
                             headers=self.headers,
                             catch_response=True,
                             timeout=30) as response:  # 搜索接口超时时间较短
            if response.status_code == 200:
                try:
                    result = response.json()
                    # 验证响应结构
                    if "results" in result and result.get("success", True):
                        response.success()
                    else:
                        response.failure(f"响应格式错误: {result}")
                except json.JSONDecodeError as e:
                    response.failure(f"JSON解析失败: {str(e)}")
            else:
                response.failure(f"HTTP {response.status_code}: {response.text[:200]}")
    
    @task(2)
    def test_health_check(self):
        """测试健康检查接口"""
        with self.client.get("/health", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")


# 自定义事件处理器
@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """测试开始时的处理"""
    logger.info("=== 压力测试开始 ===")
    logger.info(f"目标URL: {environment.host}")
    logger.info(f"用户数: {environment.runner.target_user_count if hasattr(environment.runner, 'target_user_count') else 'N/A'}")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """测试结束时的处理"""
    logger.info("=== 压力测试结束 ===")
    
    # 输出统计信息
    stats = environment.runner.stats
    logger.info(f"总请求数: {stats.total.num_requests}")
    logger.info(f"失败请求数: {stats.total.num_failures}")
    logger.info(f"平均响应时间: {stats.total.avg_response_time:.2f}ms")
    logger.info(f"最大响应时间: {stats.total.max_response_time:.2f}ms")
    logger.info(f"RPS: {stats.total.current_rps:.2f}")


if __name__ == "__main__":
    # 可以直接运行此文件进行简单测试
    print("使用以下命令运行压测:")
    print("locust -f locustfile.py --host=http://localhost:8080/api/v1")
    print("或者使用Web界面:")
    print("locust -f locustfile.py --host=http://localhost:8080/api/v1 --web-host=0.0.0.0 --web-port=8089")
