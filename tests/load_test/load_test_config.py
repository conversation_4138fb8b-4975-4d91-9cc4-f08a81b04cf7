"""
压测配置文件
定义不同场景的压测参数和配置
"""

import os
from dataclasses import dataclass
from typing import List, Dict, Any, Optional

@dataclass
class LoadTestConfig:
    """压测配置类"""
    name: str
    description: str
    users: int  # 并发用户数
    spawn_rate: float  # 用户生成速率（用户/秒）
    run_time: str  # 运行时间（如 "5m", "30s", "1h"）
    host: str  # 目标主机
    web_host: str = "0.0.0.0"
    web_port: int = 8089
    headless: bool = False  # 是否无头模式
    csv_prefix: Optional[str] = None  # CSV输出文件前缀
    html_report: Optional[str] = None  # HTML报告文件路径
    
    def to_command_args(self) -> List[str]:
        """转换为locust命令行参数"""
        args = [
            "locust",
            "-f", "locustfile.py",
            "--host", self.host,
            "--users", str(self.users),
            "--spawn-rate", str(self.spawn_rate),
            "--run-time", self.run_time,
            "--web-host", self.web_host,
            "--web-port", str(self.web_port)
        ]
        
        if self.headless:
            args.append("--headless")
        
        if self.csv_prefix:
            args.extend(["--csv", self.csv_prefix])
        
        if self.html_report:
            args.extend(["--html", self.html_report])
        
        return args

# 预定义的压测场景配置
LOAD_TEST_SCENARIOS = {
    "smoke": LoadTestConfig(
        name="冒烟测试",
        description="基础功能验证，低并发短时间测试",
        users=5,
        spawn_rate=1,
        run_time="2m",
        host=os.getenv("API_BASE_URL", "http://localhost:8080/api/v1"),
        headless=True,
        csv_prefix="results/smoke_test",
        html_report="results/smoke_test_report.html"
    ),
    
    "load": LoadTestConfig(
        name="负载测试",
        description="正常负载下的性能测试",
        users=50,
        spawn_rate=5,
        run_time="10m",
        host=os.getenv("API_BASE_URL", "http://localhost:8080/api/v1"),
        headless=True,
        csv_prefix="results/load_test",
        html_report="results/load_test_report.html"
    ),
    
    "stress": LoadTestConfig(
        name="压力测试",
        description="高并发压力测试，测试系统极限",
        users=200,
        spawn_rate=10,
        run_time="15m",
        host=os.getenv("API_BASE_URL", "http://localhost:8080/api/v1"),
        headless=True,
        csv_prefix="results/stress_test",
        html_report="results/stress_test_report.html"
    ),
    
    "spike": LoadTestConfig(
        name="峰值测试",
        description="突发流量测试",
        users=500,
        spawn_rate=50,
        run_time="5m",
        host=os.getenv("API_BASE_URL", "http://localhost:8080/api/v1"),
        headless=True,
        csv_prefix="results/spike_test",
        html_report="results/spike_test_report.html"
    ),
    
    "endurance": LoadTestConfig(
        name="持久性测试",
        description="长时间稳定性测试",
        users=100,
        spawn_rate=5,
        run_time="1h",
        host=os.getenv("API_BASE_URL", "http://localhost:8080/api/v1"),
        headless=True,
        csv_prefix="results/endurance_test",
        html_report="results/endurance_test_report.html"
    ),
    
    "interactive": LoadTestConfig(
        name="交互式测试",
        description="带Web界面的交互式测试",
        users=20,
        spawn_rate=2,
        run_time="30m",
        host=os.getenv("API_BASE_URL", "http://localhost:8080/api/v1"),
        headless=False,
        web_host="0.0.0.0",
        web_port=8089
    )
}

# 环境配置
ENVIRONMENT_CONFIGS = {
    "local": {
        "host": "http://localhost:8080/api/v1",
        "description": "本地开发环境"
    },
    "dev": {
        "host": "http://dev-api.example.com/api/v1",
        "description": "开发环境"
    },
    "staging": {
        "host": "http://staging-api.example.com/api/v1",
        "description": "预发布环境"
    },
    "prod": {
        "host": "http://api.example.com/api/v1",
        "description": "生产环境"
    }
}

# 测试数据配置
TEST_DATA_CONFIG = {
    "queries": {
        "hardware": [
            "器件距离板边最小距离",
            "PDN仿真不通过如何优化",
            "分板筋距离RF Connectors要求",
            "无工艺边拼板的要求有哪些",
            "板振问题案例",
            "FPC点击微动失效怎么解决",
            "双层FPC的归一化叠层有哪些",
            "ACLR参数定义及设计要点",
            "PCB设计基础知识",
            "HDI板相关知识"
        ],
        "data": [
            "库存周转率如何计算？",
            "什么是供应链管理？",
            "如何优化生产流程？",
            "质量控制的关键指标有哪些？",
            "成本控制的最佳实践",
            "项目管理的关键要素",
            "风险评估的方法",
            "数据分析的基本步骤"
        ],
        "general": [
            "人工智能的发展趋势",
            "机器学习的应用场景",
            "深度学习的基本原理",
            "自然语言处理技术",
            "计算机视觉应用",
            "大数据处理技术"
        ]
    },
    "model_ids": ["qwen3_32b", "qwen3_235b_2507"],
    "user_id_prefix": "load_test_user",
    "max_history_length": 5
}

# 性能阈值配置
PERFORMANCE_THRESHOLDS = {
    "response_time": {
        "p50": 2000,  # 50%请求响应时间 < 2s
        "p95": 5000,  # 95%请求响应时间 < 5s
        "p99": 10000  # 99%请求响应时间 < 10s
    },
    "error_rate": {
        "max": 0.05  # 错误率 < 5%
    },
    "throughput": {
        "min_rps": 10  # 最小RPS > 10
    }
}

# 监控配置
MONITORING_CONFIG = {
    "metrics_collection_interval": 5,  # 指标收集间隔（秒）
    "system_metrics": {
        "cpu": True,
        "memory": True,
        "disk": True,
        "network": True
    },
    "custom_metrics": {
        "response_time_percentiles": [50, 75, 90, 95, 99],
        "error_rate_window": 60,  # 错误率计算窗口（秒）
        "throughput_window": 60   # 吞吐量计算窗口（秒）
    }
}

def get_config(scenario: str, environment: str = "local") -> LoadTestConfig:
    """获取指定场景和环境的配置"""
    if scenario not in LOAD_TEST_SCENARIOS:
        raise ValueError(f"未知的测试场景: {scenario}")
    
    if environment not in ENVIRONMENT_CONFIGS:
        raise ValueError(f"未知的环境: {environment}")
    
    config = LOAD_TEST_SCENARIOS[scenario]
    config.host = ENVIRONMENT_CONFIGS[environment]["host"]
    
    return config

def list_scenarios() -> Dict[str, str]:
    """列出所有可用的测试场景"""
    return {name: config.description for name, config in LOAD_TEST_SCENARIOS.items()}

def list_environments() -> Dict[str, str]:
    """列出所有可用的环境"""
    return {name: config["description"] for name, config in ENVIRONMENT_CONFIGS.items()}

if __name__ == "__main__":
    print("可用的测试场景:")
    for name, desc in list_scenarios().items():
        print(f"  {name}: {desc}")
    
    print("\n可用的环境:")
    for name, desc in list_environments().items():
        print(f"  {name}: {desc}")
